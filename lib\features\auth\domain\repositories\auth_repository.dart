import '../entities/auth_result.dart';
import '../entities/user_entity.dart';

/// Abstract authentication repository interface
abstract class AuthRepository {
  /// Get current authenticated user
  UserEntity? get currentUser;

  /// Stream of authentication state changes
  Stream<UserEntity?> get authStateChanges;

  /// Check if user is currently signed in
  bool get isSignedIn;

  /// Sign in with email and password
  Future<AuthResult> signInWithEmailAndPassword(SignInCredentials credentials);

  /// Sign in with Google
  Future<AuthResult> signInWithGoogle();

  /// Sign out current user
  Future<void> signOut();

  /// Send password reset email
  Future<void> sendPasswordResetEmail(PasswordResetRequest request);

  /// Update user password
  Future<void> updatePassword(PasswordUpdateRequest request);

  /// Get user profile data
  Future<UserEntity?> getUserProfile(String uid);

  /// Update user profile
  Future<void> updateUserProfile(UserEntity user);

  /// Refresh current user data
  Future<UserEntity?> refreshCurrentUser();

  /// Delete user account
  Future<void> deleteAccount();

  /// Re-authenticate user (required for sensitive operations)
  Future<void> reauthenticateWithPassword(String password);

  /// Re-authenticate with Google
  Future<void> reauthenticateWithGoogle();

  /// Check if email is approved for registration
  Future<bool> isEmailApproved(String email);

  /// Verify email address
  Future<void> sendEmailVerification();

  /// Check if email is verified
  bool get isEmailVerified;

  /// Reload current user data from Firebase
  Future<void> reloadUser();
}
